// Individual Dashboard JavaScript
// Note: GSAP will be loaded via CDN in the HTML file

// Register GSAP plugins when available
document.addEventListener('DOMContentLoaded', function() {
    // Check if GSAP is available, if not, create a fallback
    if (typeof gsap === 'undefined') {
        window.gsap = {
            registerPlugin: function() {},
            from: function() {},
            to: function() {},
            timeline: function() { return { from: function() {}, to: function() {} }; }
        };
        window.ScrollTrigger = {};
    } else {
        gsap.registerPlugin(ScrollTrigger);
    }

    // Initialize the dashboard
    initializeDashboard();
    setupAnimations();
    setupInteractions();
    setupNavigation();
    setupWalletActions();
});



// Initialize Dashboard
function initializeDashboard() {
    // Set initial active page
    showPage('dashboard');
    
    // Initialize sidebar state
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const mobileSidebarToggle = document.getElementById('mobile-sidebar-toggle');
    
    // Load saved sidebar state
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (sidebarCollapsed) {
        sidebar.classList.add('collapsed');
    }
    
    // Setup sidebar toggle
    sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
        localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
    });
    
    // Setup mobile sidebar toggle
    mobileSidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('mobile-open');
    });
    
    // Close mobile sidebar when clicking outside
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768 && 
            !sidebar.contains(e.target) && 
            !mobileSidebarToggle.contains(e.target)) {
            sidebar.classList.remove('mobile-open');
        }
    });
    
    // Initialize balance visibility toggle
    setupBalanceToggle();
    
    // Initialize real-time updates
    startRealTimeUpdates();
}

// Setup GSAP Animations
function setupAnimations() {
    // Animate sidebar menu items on load
    gsap.from('.menu-item', {
        duration: 0.6,
        x: -30,
        opacity: 0,
        stagger: 0.1,
        ease: 'power3.out',
        delay: 0.3
    });
    
    // Animate balance cards
    gsap.from('.balance-card', {
        duration: 0.8,
        y: 40,
        opacity: 0,
        stagger: 0.15,
        ease: 'power3.out',
        delay: 0.5
    });
    
    // Animate quick actions
    gsap.from('.action-card', {
        duration: 0.6,
        y: 30,
        opacity: 0,
        stagger: 0.1,
        ease: 'power3.out',
        delay: 0.8
    });
    
    // Animate account cards
    gsap.from('.account-card', {
        duration: 0.8,
        y: 40,
        opacity: 0,
        stagger: 0.15,
        ease: 'power3.out',
        delay: 1
    });
    
    // Animate transaction items
    gsap.from('.transaction-item', {
        duration: 0.6,
        x: -20,
        opacity: 0,
        stagger: 0.1,
        ease: 'power3.out',
        delay: 1.2
    });
    
    // Animate numbers counting up
    animateCounters();
}

// Setup Interactions
function setupInteractions() {
    // Action card hover effects
    const actionCards = document.querySelectorAll('.action-card');
    
    actionCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1.02,
                ease: 'power2.out'
            });

            gsap.to(this.querySelector('.action-icon'), {
                duration: 0.3,
                scale: 1.1,
                ease: 'power2.out'
            });
        });

        card.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1,
                ease: 'power2.out'
            });

            gsap.to(this.querySelector('.action-icon'), {
                duration: 0.3,
                scale: 1,
                ease: 'power2.out'
            });
        });
        
        // Add click handler for actions
        card.addEventListener('click', function() {
            const action = this.dataset.action;
            handleQuickAction(action);
        });
    });
    
    // Wallet card hover effects
    const walletCards = document.querySelectorAll('.wallet-card');

    walletCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('add-wallet')) {
                gsap.to(this, {
                    duration: 0.4,
                    y: -8,
                    ease: 'power2.out'
                });
            }
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('add-wallet')) {
                gsap.to(this, {
                    duration: 0.4,
                    y: 0,
                    ease: 'power2.out'
                });
            }
        });

        // Add click handler for add wallet
        if (card.classList.contains('add-wallet')) {
            card.addEventListener('click', function() {
                showAddWalletModal();
            });
        }
    });

    // Account card hover effects (legacy)
    const accountCards = document.querySelectorAll('.account-card');

    accountCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.4,
                y: -8,
                ease: 'power2.out'
            });
        });

        card.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.4,
                y: 0,
                ease: 'power2.out'
            });
        });
    });
    
    // Balance card hover effects
    const balanceCards = document.querySelectorAll('.balance-card');
    
    balanceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            if (!this.classList.contains('primary')) {
                gsap.to(this, {
                    duration: 0.3,
                    y: -6,
                    ease: 'power2.out'
                });
            }
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('primary')) {
                gsap.to(this, {
                    duration: 0.3,
                    y: 0,
                    ease: 'power2.out'
                });
            }
        });
    });
    
    // Transaction item hover effects
    const transactionItems = document.querySelectorAll('.transaction-item');
    
    transactionItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.2,
                x: 4,
                ease: 'power2.out'
            });
        });

        item.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.2,
                x: 0,
                ease: 'power2.out'
            });
        });
    });
    
    // Button click effects
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('div');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(6, 234, 175, 0.3);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
                width: ${size}px;
                height: ${size}px;
                left: ${e.clientX - rect.left - size / 2}px;
                top: ${e.clientY - rect.top - size / 2}px;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Add ripple animation CSS if not exists
    if (!document.querySelector('#ripple-styles')) {
        const styles = document.createElement('style');
        styles.id = 'ripple-styles';
        styles.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(styles);
    }
}

// Setup Navigation
function setupNavigation() {
    const menuItems = document.querySelectorAll('.menu-item');
    
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all items
            menuItems.forEach(mi => mi.classList.remove('active'));
            
            // Add active class to clicked item
            this.classList.add('active');
            
            // Get page name
            const page = this.dataset.page;
            
            // Show corresponding page
            showPage(page);
            
            // Close mobile sidebar
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.remove('mobile-open');
            }
        });
    });
}

// Show Page Function
function showPage(pageName) {
    // Hide all pages
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => {
        page.classList.remove('active');
    });

    // Show selected page
    const targetPage = document.getElementById(`${pageName}-page`);
    if (targetPage) {
        targetPage.classList.add('active');
    }

    // Update page title
    updatePageTitle(pageName);

    // Animate page transition
    gsap.from('.page.active', {
        duration: 0.5,
        opacity: 0,
        y: 20,
        ease: 'power3.out'
    });
}

// Update Page Title
function updatePageTitle(pageName) {
    const pageTitle = document.getElementById('page-title');
    const pageSubtitle = document.getElementById('page-subtitle');
    
    const titles = {
        dashboard: {
            title: 'Dashboard',
            subtitle: 'Welcome back, John! Here\'s your financial overview.'
        },
        accounts: {
            title: 'My Accounts',
            subtitle: 'Manage your connected accounts and balances.'
        },
        transfers: {
            title: 'Transfers',
            subtitle: 'Send money to friends, family, and businesses.'
        },
        bills: {
            title: 'Bill Payments',
            subtitle: 'Pay your utilities and subscription services.'
        },
        groups: {
            title: 'Savings Groups',
            subtitle: 'Participate in group savings and investment plans.'
        },
        cards: {
            title: 'Virtual Cards',
            subtitle: 'Manage your virtual payment cards.'
        },
        analytics: {
            title: 'Analytics',
            subtitle: 'Track your spending and financial insights.'
        },
        transactions: {
            title: 'Transactions',
            subtitle: 'View your complete transaction history.'
        },
        settings: {
            title: 'Settings',
            subtitle: 'Manage your account preferences and security.'
        }
    };
    
    const pageInfo = titles[pageName] || titles.dashboard;
    pageTitle.textContent = pageInfo.title;
    pageSubtitle.textContent = pageInfo.subtitle;
}

// Handle Quick Actions
function handleQuickAction(action) {
    console.log(`Quick action: ${action}`);

    // Add loading state
    const actionCard = document.querySelector(`[data-action="${action}"]`);
    const originalContent = actionCard.innerHTML;

    actionCard.innerHTML = `
        <div class="action-icon">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
        <div class="action-content">
            <div class="action-title">Loading...</div>
            <div class="action-subtitle">Please wait</div>
        </div>
    `;

    // Simulate action processing
    setTimeout(() => {
        actionCard.innerHTML = originalContent;

        // Handle specific actions
        switch(action) {
            case 'send-money':
                showSendMoneyModal();
                break;
            case 'pay-bills':
                showPayBillsModal();
                break;
            case 'payment-request':
                showPaymentRequestModal();
                break;
            case 'top-up':
                showTopUpModal();
                break;
            case 'scan-qr':
                showQRScannerModal();
                break;
            case 'history':
                showPage('transactions');
                break;
            default:
                showNotification(`${action.replace('-', ' ')} action completed!`, 'success');
        }
    }, 1500);
}

// Setup Balance Toggle
function setupBalanceToggle() {
    const balanceToggle = document.querySelector('.balance-toggle');
    let balancesVisible = true;
    
    if (balanceToggle) {
        balanceToggle.addEventListener('click', function() {
            balancesVisible = !balancesVisible;
            
            const amounts = document.querySelectorAll('.amount, .balance-value');
            const icon = this.querySelector('i');
            
            amounts.forEach(amount => {
                if (balancesVisible) {
                    amount.style.filter = 'none';
                    icon.className = 'fas fa-eye';
                } else {
                    amount.style.filter = 'blur(8px)';
                    icon.className = 'fas fa-eye-slash';
                }
            });
        });
    }
}

// Animate Counters
function animateCounters() {
    const counters = document.querySelectorAll('.amount, .balance-value');
    
    counters.forEach(counter => {
        const target = parseFloat(counter.textContent.replace(/[^\d.]/g, ''));
        const prefix = counter.textContent.match(/[^\d.]/g)?.join('') || '';
        
        gsap.to({ value: 0 }, {
            duration: 2,
            value: target,
            ease: 'power2.out',
            onUpdate: function() {
                const currentValue = this.targets()[0].value;
                counter.textContent = prefix + currentValue.toFixed(2);
            },
            delay: 0.5
        });
    });
}

// Start Real-time Updates
function startRealTimeUpdates() {
    // Simulate real-time balance updates
    setInterval(() => {
        updateBalances();
    }, 30000); // Update every 30 seconds
    
    // Simulate new transactions
    setInterval(() => {
        addNewTransaction();
    }, 60000); // Add new transaction every minute
}

// Update Balances
function updateBalances() {
    const balanceElements = document.querySelectorAll('.amount, .balance-value');
    
    balanceElements.forEach(element => {
        const currentValue = parseFloat(element.textContent.replace(/[^\d.]/g, ''));
        const change = (Math.random() - 0.5) * 10; // Random change between -5 and +5
        const newValue = Math.max(0, currentValue + change);
        const prefix = element.textContent.match(/[^\d.]/g)?.join('') || '';
        
        gsap.to({ value: currentValue }, {
            duration: 1,
            value: newValue,
            ease: 'power2.out',
            onUpdate: function() {
                element.textContent = prefix + this.targets()[0].value.toFixed(2);
            }
        });
    });
}

// Add New Transaction
function addNewTransaction() {
    const transactionsList = document.querySelector('.transactions-list');
    if (!transactionsList) return;
    
    const newTransaction = document.createElement('div');
    newTransaction.className = 'transaction-item';
    newTransaction.style.opacity = '0';
    newTransaction.style.transform = 'translateX(-20px)';
    
    const transactions = [
        {
            icon: 'received',
            iconClass: 'fas fa-arrow-down',
            title: 'Payment received',
            subtitle: 'Just now • EcoCash',
            amount: '+$' + (Math.random() * 100 + 10).toFixed(2),
            amountClass: 'positive'
        },
        {
            icon: 'sent',
            iconClass: 'fas fa-arrow-up',
            title: 'Payment sent',
            subtitle: 'Just now • OneMoney',
            amount: '-$' + (Math.random() * 50 + 5).toFixed(2),
            amountClass: 'negative'
        }
    ];
    
    const transaction = transactions[Math.floor(Math.random() * transactions.length)];
    
    newTransaction.innerHTML = `
        <div class="transaction-icon ${transaction.icon}">
            <i class="${transaction.iconClass}"></i>
        </div>
        <div class="transaction-details">
            <div class="transaction-title">${transaction.title}</div>
            <div class="transaction-subtitle">${transaction.subtitle}</div>
        </div>
        <div class="transaction-amount ${transaction.amountClass}">${transaction.amount}</div>
        <div class="transaction-status completed">
            <i class="fas fa-check"></i>
        </div>
    `;
    
    transactionsList.insertBefore(newTransaction, transactionsList.firstChild);
    
    // Animate in
    gsap.to(newTransaction, {
        duration: 0.5,
        opacity: 1,
        x: 0,
        ease: 'power3.out'
    });
    
    // Remove oldest transaction if more than 5
    const transactions_items = transactionsList.querySelectorAll('.transaction-item');
    if (transactions_items.length > 4) {
        const lastTransaction = transactions_items[transactions_items.length - 1];
        gsap.to(lastTransaction, {
            duration: 0.3,
            opacity: 0,
            x: 20,
            ease: 'power3.in',
            onComplete: () => lastTransaction.remove()
        });
    }
}

// Show Notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add notification styles if not exists
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 100px;
                right: 20px;
                background: var(--glass-bg-strong);
                backdrop-filter: var(--glass-backdrop);
                border: 1px solid var(--glass-border);
                border-radius: var(--radius-lg);
                padding: var(--space-4);
                display: flex;
                align-items: center;
                gap: var(--space-3);
                z-index: var(--z-toast);
                min-width: 300px;
                box-shadow: var(--shadow-lg);
                transform: translateX(100%);
                transition: transform var(--transition-base);
            }
            
            .notification.show {
                transform: translateX(0);
            }
            
            .notification-success {
                border-left: 4px solid var(--color-success);
            }
            
            .notification-content {
                display: flex;
                align-items: center;
                gap: var(--space-2);
                flex: 1;
                color: var(--color-text-primary);
                font-size: var(--font-size-sm);
            }
            
            .notification-content i {
                color: var(--color-success);
            }
            
            .notification-close {
                background: none;
                border: none;
                color: var(--color-text-secondary);
                cursor: pointer;
                padding: var(--space-1);
                border-radius: var(--radius-base);
                transition: all var(--transition-base);
            }
            
            .notification-close:hover {
                background: var(--glass-bg);
                color: var(--color-text-primary);
            }
        `;
        document.head.appendChild(styles);
    }
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Auto hide after 3 seconds
    setTimeout(() => {
        hideNotification(notification);
    }, 3000);
    
    // Close button handler
    notification.querySelector('.notification-close').addEventListener('click', () => {
        hideNotification(notification);
    });
}

// Hide Notification
function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        notification.remove();
    }, 300);
}

// Resize handler
window.addEventListener('resize', function() {
    ScrollTrigger.refresh();
    
    // Close mobile sidebar on resize to desktop
    if (window.innerWidth > 768) {
        document.getElementById('sidebar').classList.remove('mobile-open');
    }
});

// Modal Functions
function showSendMoneyModal() {
    showNotification('Send Money feature coming soon!', 'info');
}

function showPayBillsModal() {
    showNotification('Pay Bills feature coming soon!', 'info');
}

function showPaymentRequestModal() {
    showNotification('Payment Request feature coming soon!', 'info');
}

function showTopUpModal() {
    showNotification('Top Up feature coming soon!', 'info');
}

function showQRScannerModal() {
    showNotification('QR Scanner feature coming soon!', 'info');
}

function showAddWalletModal() {
    showNotification('Add Wallet feature coming soon!', 'info');
}

// Wallet Actions
function setupWalletActions() {
    // Send buttons
    document.querySelectorAll('.wallet-card .btn-primary').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const walletCard = this.closest('.wallet-card');
            const walletName = walletCard.querySelector('.provider-name').textContent;
            showNotification(`Send from ${walletName} coming soon!`, 'info');
        });
    });

    // Receive buttons
    document.querySelectorAll('.wallet-card .btn-secondary').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const walletCard = this.closest('.wallet-card');
            const walletName = walletCard.querySelector('.provider-name').textContent;
            showNotification(`Receive to ${walletName} coming soon!`, 'info');
        });
    });
}

// Wallet actions are initialized in the main DOMContentLoaded listener above

// Error handling
window.addEventListener('error', function(e) {
    console.error('Dashboard Error:', e.error);
    showNotification('An error occurred. Please refresh the page.', 'error');
});
