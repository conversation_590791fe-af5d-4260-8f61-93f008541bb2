/* Individual Dashboard Styles */

/* Layout */
body {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-neutral-50), var(--color-neutral-100));
  overflow-x: hidden;
}

/* Sidebar */
.sidebar {
  width: 280px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border-right: 1px solid var(--glass-border);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: var(--z-fixed);
  transition: transform var(--transition-base);
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar.collapsed .logo-text,
.sidebar.collapsed .menu-title,
.sidebar.collapsed .menu-item span,
.sidebar.collapsed .user-info {
  opacity: 0;
  pointer-events: none;
}

.sidebar-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--glass-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.logo i {
  color: var(--color-primary);
  font-size: var(--font-size-xl);
}

.logo-text {
  transition: opacity var(--transition-base);
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-base);
  transition: all var(--transition-base);
}

.sidebar-toggle:hover {
  background: var(--glass-bg);
  color: var(--color-primary);
}

/* Sidebar Menu */
.sidebar-menu {
  flex: 1;
  padding: var(--space-6) 0;
  overflow-y: auto;
}

.menu-section {
  margin-bottom: var(--space-8);
}

.menu-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0 var(--space-6);
  margin-bottom: var(--space-4);
  transition: opacity var(--transition-base);
}

.menu-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-6);
  color: var(--color-text-secondary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base);
  position: relative;
}

.menu-item:hover {
  background: var(--glass-bg);
  color: var(--color-primary);
}

.menu-item.active {
  background: var(--color-primary-alpha);
  color: var(--color-primary);
}

.menu-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--color-primary);
}

.menu-item i {
  width: 20px;
  text-align: center;
  font-size: var(--font-size-base);
}

.menu-item span {
  transition: opacity var(--transition-base);
}

/* Sidebar Footer */
.sidebar-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--glass-border);
}

.user-profile {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  background: var(--glass-bg);
  cursor: pointer;
  transition: all var(--transition-base);
}

.user-profile:hover {
  background: var(--glass-bg-strong);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  flex: 1;
  transition: opacity var(--transition-base);
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.user-status {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

.user-menu-toggle {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-1);
  transition: color var(--transition-base);
}

.user-menu-toggle:hover {
  color: var(--color-primary);
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 280px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  transition: margin-left var(--transition-base);
}

.sidebar.collapsed + .main-content {
  margin-left: 80px;
}

/* Top Header */
.top-header {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.mobile-sidebar-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-base);
  transition: all var(--transition-base);
}

.mobile-sidebar-toggle:hover {
  background: var(--glass-bg);
  color: var(--color-primary);
}

.back-to-hub {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--color-text-secondary);
  text-decoration: none;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-base);
  transition: all var(--transition-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-right: var(--space-4);
}

.back-to-hub:hover {
  background: var(--glass-bg);
  color: var(--color-primary);
  text-decoration: none;
}

.back-to-hub i {
  font-size: var(--font-size-sm);
}

.page-title h1 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.page-title p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.header-actions {
  display: flex;
  gap: var(--space-2);
}

.action-btn {
  width: 44px;
  height: 44px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
}

.action-btn:hover {
  background: var(--glass-bg-strong);
  color: var(--color-primary);
  transform: translateY(-2px);
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--color-error);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-menu-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-2);
  cursor: pointer;
  transition: all var(--transition-base);
}

.user-menu-btn:hover {
  background: var(--glass-bg-strong);
}

.user-menu-btn img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-menu-btn i {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* Page Content */
.page-content {
  flex: 1;
  padding: var(--space-8);
  overflow-y: auto;
}

.page {
  display: none;
}

.page.active {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

/* Ensure elements are visible by default */
.menu-item,
.balance-card,
.action-card,
.account-card,
.transaction-item {
  opacity: 1;
  transform: translateX(0) translateY(0);
}



/* Section Styles */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
}

.section-header h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.view-all-link {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  transition: color var(--transition-base);
}

.view-all-link:hover {
  color: var(--color-primary-dark);
}

/* Balance Overview */
.balance-overview {
  margin-bottom: var(--space-12);
}

.balance-cards {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: var(--space-6);
}

.balance-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.balance-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
}

.balance-card.primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  color: white;
  border-color: var(--color-primary);
}

.balance-card.primary::before {
  background: rgba(255, 255, 255, 0.3);
}

.balance-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.balance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.balance-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: inherit;
  opacity: 0.8;
}

.balance-card.primary .balance-title {
  color: white;
}

.balance-toggle {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-base);
  transition: all var(--transition-base);
  opacity: 0.7;
}

.balance-toggle:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

.balance-header i {
  font-size: var(--font-size-lg);
  color: var(--color-primary);
}

.balance-card.primary .balance-header i {
  color: white;
}

.balance-amount {
  display: flex;
  align-items: baseline;
  gap: var(--space-1);
  margin-bottom: var(--space-3);
}

.currency {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  opacity: 0.8;
}

.amount {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: 1;
}

.balance-change {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.balance-change.positive {
  color: var(--color-success);
}

.balance-card.primary .balance-change.positive {
  color: rgba(255, 255, 255, 0.9);
}

.balance-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
  margin-top: var(--space-2);
}

/* Quick Actions */
.quick-actions {
  margin-bottom: var(--space-12);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.action-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  cursor: pointer;
  transition: all var(--transition-base);
  text-align: left;
}

.action-card:hover {
  background: var(--glass-bg-strong);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-neutral-900);
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.action-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* My Wallets */
.my-wallets {
  margin-bottom: var(--space-12);
}

.wallets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-6);
}

/* Connected Accounts (legacy) */
.connected-accounts {
  margin-bottom: var(--space-12);
}

.accounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-6);
}

/* Wallet Cards */
.wallet-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.wallet-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.wallet-card.ecocash::before {
  background: linear-gradient(90deg, #FF6B35, #F7931E);
}

.wallet-card.onemoney::before {
  background: linear-gradient(90deg, #1E3A8A, #3B82F6);
}

.wallet-card.bank::before {
  background: linear-gradient(90deg, #059669, #10B981);
}

.wallet-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.wallet-header {
  margin-bottom: var(--space-4);
}

.wallet-provider {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.wallet-status {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--font-size-xs);
  color: var(--color-success);
  font-weight: var(--font-weight-medium);
  margin-top: var(--space-1);
}

.wallet-status i {
  font-size: var(--font-size-xs);
}

.wallet-balance {
  margin-bottom: var(--space-6);
}

.balance-amounts {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-2);
}

.balance-separator {
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
}

.wallet-actions {
  display: flex;
  gap: var(--space-2);
}

/* Add Wallet Card */
.wallet-card.add-wallet {
  border: 2px dashed var(--glass-border);
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.wallet-card.add-wallet::before {
  display: none;
}

.wallet-card.add-wallet:hover {
  border-color: var(--color-primary);
  background: var(--glass-bg-subtle);
}

.add-wallet-content {
  text-align: center;
}

.add-wallet-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--glass-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-3);
  color: var(--color-primary);
  font-size: var(--font-size-lg);
}

.add-wallet-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.add-wallet-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Legacy Account Cards */
.account-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.account-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.account-card.ecocash::before {
  background: linear-gradient(90deg, #FF6B35, #F7931E);
}

.account-card.onemoney::before {
  background: linear-gradient(90deg, #1E3A8A, #3B82F6);
}

.account-card.bank::before {
  background: linear-gradient(90deg, #059669, #10B981);
}

.account-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.account-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
}

.account-provider {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.provider-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  color: white;
  font-size: var(--font-size-sm);
}

.account-card.ecocash .provider-icon {
  background: linear-gradient(135deg, #FF6B35, #F7931E);
}

.account-card.onemoney .provider-icon {
  background: linear-gradient(135deg, #1E3A8A, #3B82F6);
}

.account-card.bank .provider-icon {
  background: linear-gradient(135deg, #059669, #10B981);
}

.provider-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.account-number {
  font-size: var(--font-size-sm);
  color: var(--color-text-tertiary);
}

.account-status {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.account-status.connected {
  color: var(--color-success);
}

.account-balance {
  margin-bottom: var(--space-6);
}

.balance-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-2);
}

.balance-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.account-actions {
  display: flex;
  gap: var(--space-3);
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
  flex: 1;
}

/* Recent Transactions */
.recent-transactions {
  margin-bottom: var(--space-12);
}

.transactions-list {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-6);
  border-bottom: 1px solid var(--glass-border);
  transition: all var(--transition-base);
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-item:hover {
  background: var(--glass-bg-strong);
}

.transaction-icon {
  width: 44px;
  height: 44px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.transaction-icon.received {
  background: linear-gradient(135deg, var(--color-success), var(--color-success-dark));
}

.transaction-icon.sent {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
}

.transaction-icon.bill {
  background: linear-gradient(135deg, var(--color-warning), var(--color-warning-dark));
}

.transaction-icon.group {
  background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-dark));
}

.transaction-details {
  flex: 1;
}

.transaction-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--space-1);
}

.transaction-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.transaction-amount {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-right: var(--space-4);
}

.transaction-amount.positive {
  color: var(--color-success);
}

.transaction-amount.negative {
  color: var(--color-text-primary);
}

.transaction-status {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  flex-shrink: 0;
  min-width: 80px;
}

.transaction-status.completed {
  color: var(--color-success);
}

.transaction-status.pending {
  color: var(--color-warning);
}

.transaction-status.failed {
  color: var(--color-error);
}

.status-text {
  font-weight: var(--font-weight-medium);
  text-transform: lowercase;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-base);
  background: var(--glass-bg-subtle);
}

.transaction-status.completed .status-text {
  background: rgba(16, 185, 129, 0.1);
  color: var(--color-success);
}

.transaction-status.pending .status-text {
  background: rgba(245, 158, 11, 0.1);
  color: var(--color-warning);
}

.transaction-status.failed .status-text {
  background: rgba(239, 68, 68, 0.1);
  color: var(--color-error);
}

/* Legacy icon-based status */
.transaction-status i {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .balance-cards {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .accounts-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .mobile-sidebar-toggle {
    display: block;
  }

  .back-to-hub {
    display: none;
  }

  .header-actions {
    display: none;
  }

  .page-content {
    padding: var(--space-4);
  }

  .balance-cards {
    gap: var(--space-4);
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .action-card {
    padding: var(--space-4);
  }

  .accounts-grid {
    gap: var(--space-4);
  }

  .account-card {
    padding: var(--space-4);
  }

  .account-actions {
    flex-direction: column;
  }

  .transaction-item {
    padding: var(--space-4);
  }

  .transaction-amount {
    margin-right: var(--space-2);
    font-size: var(--font-size-base);
  }
}

@media (max-width: 480px) {
  .top-header {
    padding: var(--space-4);
  }

  .page-title h1 {
    font-size: var(--font-size-xl);
  }

  .balance-amount .amount {
    font-size: var(--font-size-3xl);
  }

  .action-card {
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }

  .transaction-item {
    flex-wrap: wrap;
    gap: var(--space-3);
  }

  .transaction-details {
    order: 1;
    flex: 1 1 100%;
  }

  .transaction-amount {
    order: 2;
    margin-right: 0;
  }

  .transaction-status {
    order: 3;
  }
}
