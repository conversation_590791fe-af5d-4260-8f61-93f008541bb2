{"name": "@vitejs/plugin-legacy", "version": "5.4.3", "license": "MIT", "author": "<PERSON>", "files": ["dist"], "keywords": ["frontend", "vite", "vite-plugin", "@vitejs/plugin-legacy"], "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/plugin-legacy"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://github.com/vitejs/vite/tree/main/packages/plugin-legacy#readme", "funding": "https://github.com/vitejs/vite?sponsor=1", "dependencies": {"@babel/core": "^7.25.8", "@babel/preset-env": "^7.25.8", "browserslist": "^4.24.0", "browserslist-to-esbuild": "^2.1.1", "core-js": "^3.38.1", "magic-string": "^0.30.12", "regenerator-runtime": "^0.14.1", "systemjs": "^6.15.1"}, "peerDependencies": {"terser": "^5.4.0", "vite": "^5.0.0"}, "devDependencies": {"acorn": "^8.13.0", "picocolors": "^1.1.1", "unbuild": "^2.0.0", "vite": "6.0.0-beta.5"}, "scripts": {"dev": "unbuild --stub", "build": "unbuild && pnpm run patch-cjs", "patch-cjs": "tsx ../../scripts/patchCJS.ts"}}